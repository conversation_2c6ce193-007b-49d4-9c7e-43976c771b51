import dotenv from 'dotenv';
dotenv.config();

// Runtime environment detection
export const RUNTIME_ENV = process.env.RUNTIME_ENV || 'local';

// General configuration
export const OUTPUT_DIR = process.env.OUTPUT_DIR || null;
export const MAX_RETRY_COUNT = parseInt(process.env.MAX_RETRY_COUNT, 10) || 2;
export const RETRY_DELAY = parseInt(process.env.RETRY_DELAY, 10) || 1000;

// Browser args for all environments
const browserArgs = [
  // Security and sandboxing
  '--no-sandbox',
  '--disable-setuid-sandbox',
  '--disable-dev-shm-usage',

  // Performance optimizations
  '--disable-gpu',
  '--disable-background-timer-throttling',
  '--disable-backgrounding-occluded-windows',
  '--disable-renderer-backgrounding',
  '--disable-features=TranslateUI',
  '--disable-ipc-flooding-protection',

  // Visual rendering optimizations
  '--disable-extensions',
  '--disable-plugins',
  '--disable-default-apps',
  '--disable-sync',
  '--no-first-run',

  // Network optimizations
  '--aggressive-cache-discard',
  '--disable-background-networking',

  // Memory optimizations
  '--memory-pressure-off',
  '--disable-web-security', // Only for scraping
];

// Browser options
export const BROWSER_OPTIONS = {
  headless: process.env.HEADLESS !== 'false',
  ignoreHTTPSErrors: true,
  args: browserArgs,
  viewport: {
    width: parseInt(process.env.VIEWPORT_WIDTH, 10) || 1920,
    height: parseInt(process.env.VIEWPORT_HEIGHT, 10) || 1080,
  },
};

// Page navigation options
export const PAGE_OPTIONS = {
  waitUntil: 'networkidle',
  timeout: parseInt(process.env.PAGE_TIMEOUT, 10) || 30000,
};

// Performance-focused page options
export const PERFORMANCE_PAGE_OPTIONS = {
  waitUntil: 'domcontentloaded',
  timeout: parseInt(process.env.PAGE_TIMEOUT, 10) || 20000,
};

// LLM Model configuration
export const LLM_MODEL_CONFIG = {
  apiKey: process.env.GOOGLE_AI_API_KEY || '',
  modelName: 'gemini-2.5-flash',
  smallModel: 'gemini-2.5-flash',
  maxTokens: 32768,
  temperature: 0.1, // Lower temperature for consistent results
};

// Streaming configuration
export const STREAMING_CONFIG = {
  enabled: process.env.STREAMING_ENABLED !== 'false',
  bufferSize: parseInt(process.env.STREAM_BUFFER_SIZE, 10) || 1024,
  heartbeatInterval: parseInt(process.env.STREAM_HEARTBEAT, 10) || 5000, // 5 seconds
  maxStreamDuration: parseInt(process.env.MAX_STREAM_DURATION, 10) || 600000, // 10 minutes
};

// File handling configuration
export const FILE_CONFIG = {
  maxFileSize: parseInt(process.env.MAX_FILE_SIZE, 10) || 50 * 1024 * 1024, // 50MB
  tempDir: process.env.TEMP_DIR || './temp',
  cleanupTempFiles: process.env.CLEANUP_TEMP !== 'false',
};

// Export environment detection
export const IS_PRODUCTION = process.env.NODE_ENV === 'production';
export const IS_DEVELOPMENT =
  process.env.NODE_ENV === 'development' || !process.env.NODE_ENV;
