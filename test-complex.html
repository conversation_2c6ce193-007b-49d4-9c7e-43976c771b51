Shadcn Tooltip - Shadcn Studio
<div></div>
<div>
  <div>
    <div>
      <div>
        <div><span>Sidebar</span><a><div><span>shadcn/studio</span></div></a>v1.0.0-beta.3</div>
        <div><a class="">Theme Generator</a><a class="">Docs</a><a class="">Components</a></div>
        <div><span>Search...</span><span>Search</span><kbd><span>⌘</span>K</kbd>
          <div>
            <h2 id="user-content-radix-«R8qdmdbH1»" class="">Command Palette</h2>
            <p id="user-content-radix-«R8qdmdbH2»">Search for a command to run...</p>
          </div><span>Toggle Theme</span>
          <div><a class=""><span>Github</span></a><a class=""><span>X</span></a><a class=""><span>Discord</span></a></div>
        </div>
      </div>
    </div>
    <div>
      <div>
        <div>
          <div>
            <div>
              <div>
                <div>
                  <div>
                    <ul class="">
                      <li class=""><a class="">Theme Generator<div>Hot</div></a></li>
                      <li class=""><a class="">Components</a></li>
                      <li class="">Figma UI Kit
                        <div>Coming Soon</div>
                      </li>
                      <li class="">Blocks
                        <div>Coming Soon</div>
                      </li>
                      <li class="">Templates
                        <div>Coming Soon</div>
                      </li>
                    </ul>
                  </div>
                  <div>
                    <ul class="">
                      <div>
                        <li class=""><span>Getting Started</span>
                          <div id="user-content-radix-«Rkplmdb»">
                            <ul class="">
                              <li class=""><a class="">Introduction</a></li>
                            </ul>
                          </div>
                        </li>
                      </div>
                    </ul>
                  </div>
                  <div>
                    <ul class="">
                      <div>
                        <li class=""><span>Animated Components</span>
                          <div id="user-content-radix-«Rsplmdb»">
                            <ul class="">
                              <li class=""><a class="">Button</a></li>
                              <li class=""><a class="">Button Group</a></li>
                              <li class=""><a class="">Card</a></li>
                              <li class=""><a class="">Checkbox</a></li>
                              <li class=""><a class="">Collapsible</a></li>
                              <li class=""><a class="">Combobox</a></li>
                              <li class=""><a class="">Dialog</a></li>
                              <li class=""><a class="">Dropdown Menu</a></li>
                              <li class=""><a class="">Popover</a></li>
                              <li class=""><a class="">Radio Group</a></li>
                              <li class=""><a class="">Select</a></li>
                              <li class=""><a class="">Switch</a></li>
                              <li class=""><a class="">Tabs<div>New</div></a></li>
                              <li class=""><a class="">Tooltip</a></li>
                            </ul>
                          </div>
                        </li>
                      </div>
                    </ul>
                  </div>
                  <div>
                    <ul class="">
                      <div>
                        <li class=""><span>Components</span>
                          <div id="user-content-radix-«R14plmdb»">
                            <ul class="">
                              <li class=""><a class="">Accordion</a></li>
                              <li class=""><a class="">Alert</a></li>
                              <li class=""><a class="">Avatar</a></li>
                              <li class=""><a class="">Badge</a></li>
                              <li class=""><a class="">Breadcrumb</a></li>
                              <li class=""><a class="">Button</a></li>
                              <li class=""><a class="">Button Group</a></li>
                              <li class=""><a class="">Calendar</a></li>
                              <li class=""><a class="">Card</a></li>
                              <li class=""><a class="">Checkbox</a></li>
                              <li class=""><a class="">Collapsible</a></li>
                              <li class=""><a class="">Combobox</a></li>
                              <li class=""><a class="">Data Table</a></li>
                              <li class=""><a class="">Date and Time Picker</a></li>
                              <li class=""><a class="">Dialog</a></li>
                              <li class=""><a class="">Dropdown Menu</a></li>
                              <li class=""><a class="">Form</a></li>
                              <li class=""><a class="">Input</a></li>
                              <li class=""><a class="">Input Mask</a></li>
                              <li class=""><a class="">Input OTP</a></li>
                              <li class=""><a class="">Pagination<div>New</div></a></li>
                              <li class=""><a class="">Popover</a></li>
                              <li class=""><a class="">Radio Group</a></li>
                              <li class=""><a class="">Select</a></li>
                              <li class=""><a class="">Sheet<div>New</div></a></li>
                              <li class=""><a class="">Sonner<div>New</div></a></li>
                              <li class=""><a class="">Switch</a></li>
                              <li class=""><a class="">Table</a></li>
                              <li class=""><a class="">Tabs<div>New</div></a></li>
                              <li class=""><a class="">Textarea</a></li>
                              <li class=""><a class="">Tooltip</a></li>
                              <li class=""><a class="">Carousel<div>Coming Soon</div></a></li>
                              <li class=""><a class="">Chart<div>Coming Soon</div></a></li>
                              <li class=""><a class="">Command<div>Coming Soon</div></a></li>
                              <li class=""><a class="">Context Menu<div>Coming Soon</div></a></li>
                              <li class=""><a class="">Drawer<div>Coming Soon</div></a></li>
                              <li class=""><a class="">Menubar<div>Coming Soon</div></a></li>
                              <li class=""><a class="">Navigation Menu<div>Coming Soon</div></a></li>
                              <li class=""><a class="">Progress<div>Coming Soon</div></a></li>
                              <li class=""><a class="">Separator<div>Coming Soon</div></a></li>
                              <li class=""><a class="">Sidebar<div>Coming Soon</div></a></li>
                              <li class=""><a class="">Skeleton<div>Coming Soon</div></a></li>
                              <li class=""><a class="">Slider<div>Coming Soon</div></a></li>
                              <li class=""><a class="">Toggle<div>Coming Soon</div></a></li>
                              <li class=""><a class="">Toggle Group<div>Coming Soon</div></a></li>
                            </ul>
                          </div>
                        </li>
                      </div>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div id="user-content-main-section">
          <div>
            <div>
              <h1>Shadcn Tooltip</h1>
              <p>Elevate your UI with a growing collection of 15 Shadcn tooltip components, built using React and Tailwind CSS.</p>
            </div>
            <div>
              <div>
                <div>Default
                  <div>
                    <div></div>
                    <div></div><span>View code</span>
                  </div>
                </div>
              </div>
              <div>
                <div>Light
                  <div>
                    <div></div>
                    <div></div><span>View code</span>
                  </div>
                </div>
              </div>
              <div>
                <div>No arrow
                  <div>
                    <div></div>
                    <div></div><span>View code</span>
                  </div>
                </div>
              </div>
              <div>
                <div>Error
                  <div>
                    <div></div>
                    <div></div><span>View code</span>
                  </div>
                </div>
              </div>
              <div>
                <div>Icon
                  <div>
                    <div></div>
                    <div></div><span>View code</span>
                  </div>
                </div>
              </div>
              <div>
                <div>Rounded
                  <div>
                    <div></div>
                    <div></div><span>View code</span>
                  </div>
                </div>
              </div>
              <div>
                <div>Content
                  <div>
                    <div></div>
                    <div></div><span>View code</span>
                  </div>
                </div>
              </div>
              <div>
                <div>Avatar
                  <div>
                    <div></div>
                    <div></div><span>View code</span>
                  </div>
                </div>
              </div>
              <div>
                <div>Badge
                  <div>
                    <div></div>
                    <div></div><span>View code</span>
                  </div>
                </div>
              </div>
              <div>
                <div>
                  <div>LeftTopBottomRight</div>
                  <div>
                    <div></div>
                    <div></div><span>View code</span>
                  </div>
                </div>
              </div>
              <div>
                <div>Hover Card Media
                  <div>
                    <div></div>
                    <div></div><span>View code</span>
                  </div>
                </div>
              </div>
              <div>
                <div>Hover Card Stats
                  <div>
                    <div></div>
                    <div></div><span>View code</span>
                  </div>
                </div>
              </div>
              <div>
                <div>Hover Card Project
                  <div>
                    <div></div>
                    <div></div><span>View code</span>
                  </div>
                </div>
              </div>
              <div>
                <div>Hover Card Alert
                  <div>
                    <div></div>
                    <div></div><span>View code</span>
                  </div>
                </div>
              </div>
              <div>
                <div>Hover Card Tasks
                  <div>
                    <div></div>
                    <div></div><span>View code</span>
                  </div>
                </div>
              </div>
            </div>
            <div id="user-content-animated-variants">
              <h2 class="">Animated Tooltip</h2>
              <p>Enhance your interface with 2 animated tooltip components, crafted with React, Tailwind CSS, and Motion for smooth, interactive animations.</p>
            </div>
            <div>
              <div>
                <div>
                  <div>
                    <div>
                      <div><span><img></span></div>
                      <div><span><img></span></div>
                      <div><span><img></span></div>
                      <div><span><img></span></div>
                    </div>
                    <p>Inspired by <a class="">Aceternity UI</a></p>
                  </div>
                  <div>
                    <div></div>
                    <div></div><span>View code</span>
                  </div>
                </div>
              </div>
              <div>
                <div>
                  <div>
                    <div><span><img></span><span><img></span><span><img></span><span><img></span></div>
                    <p>Inspired by <a class="">Animate UI</a></p>
                  </div>
                  <div>
                    <div></div>
                    <div></div><span>View code</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div><span>Scroll to top</span>
      </div>
    </div>
    <div>
      <div>
        <div><a><div><span>shadcn/studio</span></div></a>
          <p>An open-source collection of copy-and-paste shadcn components, blocks, and templates - paired with a powerful theme generator to craft, customize, and ship faster.</p>
          <div><a><span>Github</span></a><a><span>Discord</span></a><a><span>X</span></a><a><span>Youtube</span></a></div>
          <p>This project is independent and not affiliated with Figma or shadcn/ui.</p>
        </div>
        <div>
          <div>
            <div>Products</div>
            <ul class="">
              <li><a>Shadcn Theme Generator</a></li>
              <li><a>Shadcn Components</a></li>
              <li><a>Shadcn Figma UI Kit <span>(Soon)</span></a></li>
              <li><a>Shadcn Blocks <span>(Soon)</span></a></li>
              <li><a>Shadcn Templates <span>(Soon)</span></a></li>
            </ul>
          </div>
          <div>
            <div>Resources</div>
            <ul class="">
              <li><a>Roadmap</a></li>
              <li><a>Changelog</a></li>
            </ul>
          </div>
          <div>
            <div>Legal</div>
            <ul class="">
              <li><a>License</a></li>
              <li><a>Privacy policy</a></li>
              <li><a>Terms &#x26; Condition</a></li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div>
      <div>
        <div>
          <p>Innovative Offerings</p>
          <p>Discover other Tools &#x26; Brands in our Ecosystem</p>
        </div>
        <div><a><span>ThemeSelection</span></a><a><span>FlyonUI</span></a><a><span>All UtilityCSS</span></a><a><span>All ShadCN</span></a><a><span>PixInvent</span></a><a><span>JetShip</span></a></div>
      </div>
    </div>
    <div>
      <div>
        <p>©2025 <a class="">shadcn/studio</a>, Supported by <a class="">ThemeSelection</a></p>
        <p>Building in public by <a class="">@Ajay Patel</a>, designed by <a class="">@Anand Patel</a></p>
      </div>
    </div>
  </div>
</div>
<section></section>
