#!/usr/bin/env node

import { scrape, SCRAPING_MODES } from '../WebScraper.js';

// CLI Help text
const HELP_TEXT = `
Context Scraper - A comprehensive web scraper built with Playwright

Usage:
  node src/index.js <url> [options]
  npm start <url> [options]

Arguments:
  url                   The URL to scrape (required)

Options:
  -o, --output <file>   Output filename (default: scraped)
  -q, --query <text>    Optional user query for focused content extraction
  -m, --mode <mode>     Scraping mode: normal or beast (default: beast)
  -p, --performance     Enable detailed performance monitoring (always enabled)
  -h, --help           Show this help message

Examples:
  node src/index.js https://example.com
  node src/index.js https://example.com -o output
  node src/index.js https://example.com -q "Find pricing information"
  node src/index.js https://example.com -o custom-output -q "Extract product details" -m normal
  npm start https://example.com -- -o custom-output -q "Get contact information"

Description:
  This scraper extracts content from web pages, including:
  - Static HTML content
  - Dynamic content revealed by user interactions
  - Lazy-loaded content via scrolling
  - Content from interactive elements like tabs, accordions, etc.

  Performance monitoring is automatically enabled to track:
  - CPU and memory usage throughout the process
  - Browser launch and page load times
  - Network request counts
  - Phase-by-phase performance breakdown
  - Detailed reports with optimization recommendations

Modes:
  normal - Simple extraction using page.content() only
  beast  - Advanced extraction with AI-powered interactive element detection (default)

The scraper uses AI to identify interactive elements and processes them
to reveal hidden content, then combines everything into a clean output.

The optional query parameter helps focus the AI on specific content types
or sections you're interested in extracting.
`;

// Parse command line arguments
function parseArgs(args) {
  const config = {
    url: null,
    output: 'scraped',
    query: '',
    mode: 'beast',
    showHelp: false,
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];

    if (arg === '-h' || arg === '--help') {
      config.showHelp = true;
    } else if (arg === '-o' || arg === '--output') {
      if (i + 1 < args.length) {
        config.output = args[i + 1];
        i++; // Skip next argument as it's the output filename
      } else {
        console.error('Error: --output option requires a filename');
        process.exit(1);
      }
    } else if (arg === '-q' || arg === '--query') {
      if (i + 1 < args.length) {
        config.query = args[i + 1];
        i++; // Skip next argument as it's the query text
      } else {
        console.error('Error: --query option requires a text value');
        process.exit(1);
      }
    } else if (arg === '-m' || arg === '--mode') {
      if (i + 1 < args.length) {
        const mode = args[i + 1].toLowerCase();
        if (mode === 'normal' || mode === 'beast') {
          config.mode = mode;
        } else {
          console.error('Error: --mode must be either "normal" or "beast"');
          process.exit(1);
        }
        i++; // Skip next argument as it's the mode value
      } else {
        console.error(
          'Error: --mode option requires a value (normal or beast)'
        );
        process.exit(1);
      }
    } else if (!config.url && !arg.startsWith('-')) {
      config.url = arg;
    }
  }

  return config;
}

// Validate URL
function isValidUrl(string) {
  try {
    new URL(string);
    return true;
  } catch (error) {
    console.error('Error: Invalid URL provided', error);
    console.error(
      'Please provide a valid URL starting with http:// or https://'
    );
    return false;
  }
}

// Main CLI function
async function main() {
  const args = process.argv.slice(2);
  const config = parseArgs(args);

  // Show help if requested or no arguments provided
  if (config.showHelp || args.length === 0) {
    console.log(HELP_TEXT);
    process.exit(0);
  }

  // Validate required arguments
  if (!config.url) {
    console.error('Error: URL is required');
    console.log('\nUse --help for usage information');
    process.exit(1);
  }

  if (!isValidUrl(config.url)) {
    console.error('Error: Invalid URL provided');
    console.error(
      'Please provide a valid URL starting with http:// or https://'
    );
    process.exit(1);
  }

  console.log('🚀 Starting Context Scraper...');
  console.log(`📄 URL: ${config.url}`);
  console.log(`💾 Output: ${config.output}`);
  console.log(`🎯 Mode: ${config.mode}`);
  if (config.query) {
    console.log(`🔍 Query: ${config.query}`);
  }
  console.log('');

  // Convert mode string to constant
  const mode =
    config.mode === 'normal' ? SCRAPING_MODES.NORMAL : SCRAPING_MODES.BEAST;

  try {
    const success = await scrape(config.url, config.output, config.query, mode);

    if (success) {
      console.log(`📁 Output saved to: ${config.output}.md`);
      if (config.mode === 'normal') {
        console.log(`📁 HTML saved to: ${config.output}.html`);
      }
    } else {
      console.log('');
      console.error('❌ Scraping failed after all retry attempts');
      process.exit(1);
    }
  } catch (error) {
    console.log('');
    console.error('❌ Scraping failed with error:', error.message);
    process.exit(1);
  }
}

// Handle uncaught errors
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

process.on('uncaughtException', error => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// Run the CLI
main().catch(error => {
  console.error('CLI Error:', error);
  process.exit(1);
});
